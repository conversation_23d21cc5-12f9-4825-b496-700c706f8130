"use client"

import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Separator } from '@/components/ui/separator'
import {
  BookOpen,
  LayoutDashboard,
  PlusCircle,
  FileText,
  Users,
  Settings,
  HelpCircle,
  ChevronLeft,
  Folder,
  BarChart3,
} from 'lucide-react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { useState } from 'react'
import type { ComponentType } from 'react'

interface SidebarProps {
  className?: string
  isOpen?: boolean
  onClose?: () => void
}

const navigation = [
  {
    title: 'Overview',
    items: [
      {
        title: 'Dashboard',
        href: '/dashboard',
        icon: LayoutDashboard,
        description: 'Writing analytics and insights'
      },
      {
        title: 'All Projects', 
        href: '/projects',
        icon: Folder,
        description: 'Browse all your projects'
      }
    ]
  },
  {
    title: 'Create',
    items: [
      {
        title: 'New Project',
        href: '/projects/new',
        icon: PlusCircle,
        description: 'Start a new writing project'
      }
    ]
  },
  {
    title: 'Tools',
    items: [
      {
        title: 'Templates',
        href: '/templates',
        icon: FileText,
        description: 'Project templates and presets'
      },
      {
        title: 'Character Manager',
        href: '/characters',
        icon: Users,
        description: 'Manage your characters'
      }
    ]
  },
  {
    title: 'Account',
    items: [
      {
        title: 'Settings',
        href: '/settings',
        icon: Settings,
        description: 'Account and preferences'
      },
      {
        title: 'Help & Support',
        href: '/help',
        icon: HelpCircle,
        description: 'Documentation and support'
      }
    ]
  }
]

interface NavItemProps {
  href: string
  icon: ComponentType<{ className?: string }>
  title: string
  description: string
  isActive: boolean
  isCollapsed: boolean
  onClick?: () => void
}

function NavItem({ href, icon: Icon, title, description, isActive, isCollapsed, onClick }: NavItemProps) {
  return (
    <Button
      asChild
      variant={isActive ? 'secondary' : 'ghost'}
      className={cn(
        'w-full justify-start h-auto p-3',
        isActive && 'bg-secondary',
        isCollapsed && 'justify-center p-2'
      )}
      onClick={onClick}
    >
      <Link href={href}>
        <Icon className={cn('h-4 w-4', !isCollapsed && 'mr-3')} />
        {!isCollapsed && (
          <div className="flex flex-col items-start">
            <span className="text-sm font-medium">{title}</span>
            <span className="text-xs text-muted-foreground line-clamp-1">
              {description}
            </span>
          </div>
        )}
      </Link>
    </Button>
  )
}

export function Sidebar({ className, onClose }: SidebarProps) {
  const pathname = usePathname()
  const [isCollapsed, setIsCollapsed] = useState(false)
  
  const toggleCollapsed = () => {
    setIsCollapsed(!isCollapsed)
  }
  
  return (
    <div className={cn(
      'relative flex flex-col h-full bg-background border-r',
      isCollapsed ? 'w-16' : 'w-64',
      className
    )}>
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b">
        {!isCollapsed && (
          <Link href="/dashboard" className="flex items-center space-x-2" onClick={onClose}>
            <BookOpen className="h-5 w-5" />
            <span className="font-semibold">BookScribe AI</span>
          </Link>
        )}
        
        <Button
          variant="ghost"
          size="icon"
          onClick={toggleCollapsed}
          className={cn(
            'h-8 w-8',
            isCollapsed && 'mx-auto'
          )}
          aria-label={isCollapsed ? 'Expand sidebar' : 'Collapse sidebar'}
        >
          <ChevronLeft className={cn(
            'h-4 w-4 transition-transform',
            isCollapsed && 'rotate-180'
          )} />
          <span className="sr-only">{isCollapsed ? 'Expand sidebar' : 'Collapse sidebar'}</span>
        </Button>
      </div>
      
      {/* Navigation */}
      <ScrollArea className="flex-1 px-3 py-4">
        <div className="space-y-6">
          {navigation.map((section) => (
            <div key={section.title}>
              {!isCollapsed && (
                <h4 className="mb-2 px-3 text-xs font-semibold text-muted-foreground uppercase tracking-wider">
                  {section.title}
                </h4>
              )}
              <div className="space-y-1">
                {section.items.map((item) => (
                  <NavItem
                    key={item.href}
                    href={item.href}
                    icon={item.icon}
                    title={item.title}
                    description={item.description}
                    isActive={pathname === item.href || pathname.startsWith(item.href + '/')}
                    isCollapsed={isCollapsed}
                    onClick={onClose}
                  />
                ))}
              </div>
              {!isCollapsed && <Separator className="mt-4" />}
            </div>
          ))}
        </div>
      </ScrollArea>
      
      {/* Footer */}
      <div className="p-4 border-t">
        {!isCollapsed ? (
          <div className="text-xs text-muted-foreground space-y-1">
            <p>BookScribe AI v1.0</p>
            <p>AI-Powered Novel Writing</p>
          </div>
        ) : (
          <div className="flex justify-center">
            <BookOpen className="h-4 w-4 text-muted-foreground" />
          </div>
        )}
      </div>
    </div>
  )
}