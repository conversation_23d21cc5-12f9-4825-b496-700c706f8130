'use client'

import { useEffect, useState } from 'react'
import { logger } from '@/lib/services/logger';

import Link from 'next/link'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Skeleton } from '@/components/ui/skeleton'
import { Plus, BookOpen, AlertCircle } from 'lucide-react'

interface Project {
  id: string
  name?: string  // Might be name or title
  title?: string
  description: string | null
  genre?: string
  primary_genre?: string
  status: string
  progress?: {
    wordsWritten: number
    targetWords: number
    percentComplete: number
  }
  current_word_count?: number
  target_word_count?: number | null
}

interface SimpleProjectsListProps {
  userId: string
}

export function SimpleProjectsList({ userId }: SimpleProjectsListProps) {
  const [projects, setProjects] = useState<Project[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchProjects = async () => {
      try {
        setLoading(true)
        const response = await fetch('/api/projects')
        if (!response.ok) {
          throw new Error('Failed to fetch projects')
        }
        const data = await response.json()
        setProjects(data.projects || [])
      } catch (err) {
        logger.error('Error fetching projects:', err);
        setError(err instanceof Error ? err.message : 'Failed to load projects')
      } finally {
        setLoading(false)
      }
    }

    fetchProjects()
  }, [])

  if (loading) {
    return (
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {[1, 2, 3].map((i) => (
          <Card key={i}>
            <CardHeader>
              <Skeleton className="h-6 w-3/4" />
              <Skeleton className="h-4 w-1/2" />
            </CardHeader>
            <CardContent>
              <Skeleton className="h-12 w-full mb-4" />
              <div className="flex justify-between">
                <Skeleton className="h-4 w-20" />
                <Skeleton className="h-4 w-24" />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }

  if (error) {
    return (
      <Card className="border-destructive">
        <CardContent className="py-16 text-center">
          <AlertCircle className="h-12 w-12 mx-auto mb-4 text-destructive" />
          <p className="text-destructive mb-4">{error}</p>
          <Button onClick={() => window.location.reload()} variant="outline">
            Try Again
          </Button>
        </CardContent>
      </Card>
    )
  }

  if (projects.length === 0) {
    return (
      <Card className="text-center border-border bg-card backdrop-blur-sm">
        <CardContent className="py-16">
          <BookOpen className="h-16 w-16 mx-auto mb-4 text-muted-foreground/50" />
          <p className="mb-6 text-muted-foreground font-literary text-lg">
            Your literary journey begins here.
          </p>
          <div className="flex gap-3 justify-center flex-wrap">
            <Link href="/templates">
              <Button variant="outline">Browse Templates</Button>
            </Link>
            <Link href="/samples">
              <Button variant="outline">Try a Sample</Button>
            </Link>
            <Link href="/projects/new">
              <Button variant="literary">
                <Plus className="h-4 w-4 mr-2" />
                Create Your First Project
              </Button>
            </Link>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
      {projects.map((project) => {
        const title = project.title || project.name || 'Untitled'
        const genre = project.primary_genre || project.genre || 'General'
        const wordCount = project.current_word_count || project.progress?.wordsWritten || 0
        const targetWords = project.target_word_count || project.progress?.targetWords || 0
        
        return (
          <Link key={project.id} href={`/projects/${project.id}/write`}>
            <Card className="h-full border-border bg-card transition-all hover:shadow-lg hover:border-primary/30">
              <CardHeader>
                <CardTitle>{title}</CardTitle>
                <CardDescription>
                  {genre} • {targetWords ? targetWords.toLocaleString() : '?'} words
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground line-clamp-3">
                  {project.description || 'No description yet'}
                </p>
                <div className="mt-4 flex items-center justify-between text-sm">
                  <Badge 
                    variant={
                      project.status === 'completed' ? 'default' :
                      project.status === 'in_progress' ? 'secondary' :
                      'outline'
                    }
                  >
                    {project.status.replace('_', ' ')}
                  </Badge>
                  <span className="text-muted-foreground">
                    {wordCount.toLocaleString()} / {targetWords ? targetWords.toLocaleString() : '?'}
                  </span>
                </div>
                {project.progress && (
                  <div className="mt-2">
                    <div className="h-2 w-full bg-secondary rounded-full overflow-hidden">
                      <div 
                        className="h-full bg-primary transition-all"
                        style={{ width: `${Math.min(project.progress.percentComplete || 0, 100)}%` }}
                      />
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </Link>
        )
      })}
    </div>
  )
}