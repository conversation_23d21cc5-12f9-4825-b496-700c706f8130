'use client';

import { lazy, Suspense } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';

// Dynamically import the Monaco Editor component
const UnifiedMonacoEditor = lazy(() => 
  import('./unified-monaco-editor').then(module => ({ 
    default: module.UnifiedMonacoEditor 
  }))
);

// Loading fallback component
function MonacoEditorSkeleton() {
  return (
    <Card className="h-full">
      <CardContent className="p-6 h-full">
        <div className="space-y-4 h-full">
          <div className="flex items-center justify-between">
            <Skeleton className="h-8 w-32" />
            <div className="flex space-x-2">
              <Skeleton className="h-8 w-20" />
              <Skeleton className="h-8 w-20" />
              <Skeleton className="h-8 w-20" />
            </div>
          </div>
          <Skeleton className="h-full w-full min-h-96" />
          <div className="flex items-center justify-between">
            <Skeleton className="h-6 w-48" />
            <Skeleton className="h-6 w-24" />
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

// Dynamic wrapper component
// Import the props type from the unified editor
type UnifiedMonacoEditorProps = {
  value?: string;
  initialContent?: string;
  onChange?: (value: string) => void;
  onContentChange?: (content: string) => void;
  onSave?: () => void;
  readOnly?: boolean;
  height?: string;
  showToolbar?: boolean;
  showStats?: boolean;
  showAISuggestions?: boolean;
  showSelectionMenu?: boolean;
  enableRealTimeAnalysis?: boolean;
  projectId?: string;
  chapterNumber?: number;
  mode?: 'basic' | 'advanced';
  focusMode?: boolean;
  onFocusModeToggle?: (enabled: boolean) => void;
};

export function DynamicMonacoEditor(props: UnifiedMonacoEditorProps) {
  return (
    <Suspense fallback={<MonacoEditorSkeleton />}>
      <UnifiedMonacoEditor {...props} />
    </Suspense>
  );
}

export default DynamicMonacoEditor;