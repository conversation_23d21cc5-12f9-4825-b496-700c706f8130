# Profile Settings Database Setup

## Current Status ✅
Your authentication setup is **correct**:
- ✅ Supabase `auth.users` table (managed automatically)
- ✅ `profiles` table exists
- ✅ `projects` table exists for stats
- ✅ Row Level Security (RLS) policies

## Missing Fields ⚠️
Your `profiles` table needs additional columns for the full profile functionality:

**Current fields:**
- id, email, full_name, avatar_url, stripe_customer_id, created_at, updated_at

**Missing fields needed:**
- username, bio, location, website, writing_goals, preferences

## Quick Fix - Run Migration

### Option 1: Automatic Script (Recommended)
```bash
# Make sure you have SUPABASE_SERVICE_ROLE_KEY in your .env.local
node scripts/update-profile-schema.js
```

### Option 2: Manual (Supabase Dashboard)
1. Go to your Supabase dashboard
2. Navigate to **SQL Editor**
3. Copy and paste the contents of `supabase/migrations/002_profile_enhancements.sql`
4. Click **Run**

### Option 3: Supabase CLI
```bash
# If you have Supabase CLI installed
supabase db push
```

## What This Adds

### New Profile Fields:
- **username** - Unique username for the user
- **bio** - Personal bio/description
- **location** - User's location (City, Country)
- **website** - Personal website URL
- **writing_goals** - JSON object with daily_words, weekly_hours, genre_focus
- **preferences** - JSON object with privacy and notification settings

### Automatic Features:
- **Auto-profile creation** - Creates profile when user signs up
- **Username indexing** - Fast username lookups
- **Enhanced RLS** - Proper security policies

## Verify Setup
After running the migration, check your Supabase dashboard:
1. Go to **Table Editor** → **profiles**
2. You should see the new columns
3. Test creating a user account - profile should auto-create

## Test Profile Settings
1. Sign up for a new account or log in
2. Go to Settings → Profile tab
3. You should be able to:
   - Edit profile information
   - Set writing goals
   - Configure privacy preferences
   - View writing statistics

## Troubleshooting

### If migration fails:
- Check you have `SUPABASE_SERVICE_ROLE_KEY` in `.env.local`
- Try the manual SQL method in Supabase dashboard
- Ensure you have proper permissions on your Supabase project

### If profile settings don't load:
- Check browser console for errors
- Verify RLS policies are applied
- Test with a fresh user signup

## Migration File Location
```
📁 supabase/migrations/002_profile_enhancements.sql
📁 scripts/update-profile-schema.js
```

Your authentication is properly set up! You just need these additional profile fields to enable the full profile functionality.