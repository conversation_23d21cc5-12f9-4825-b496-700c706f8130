/* Knowledge Demo Specific Styling */

/* Ensure Writer's Sanctuary theme is properly applied */
.knowledge-demo-container {
  background: hsl(var(--background));
  color: hsl(var(--foreground));
}

/* Override any Monaco editor dark theme */
.monaco-editor {
  background: hsl(var(--background)) !important;
  color: hsl(var(--foreground)) !important;
}

.monaco-editor .margin {
  background: hsl(var(--card)) !important;
}

.monaco-editor .monaco-editor-background {
  background: hsl(var(--background)) !important;
}

/* Ensure panels have proper theme colors */
.knowledge-demo-panel {
  background: hsl(var(--card));
  color: hsl(var(--card-foreground));
  border-color: hsl(var(--border));
}

/* Chapter navigator styling */
.chapter-navigator {
  background: hsl(var(--card));
  color: hsl(var(--card-foreground));
  border-color: hsl(var(--border));
}

/* Sidebar panel styling */
.sidebar-panel {
  background: hsl(var(--card));
  color: hsl(var(--card-foreground));
  border-color: hsl(var(--border));
}

/* Header styling */
.demo-header {
  background: hsl(var(--card));
  color: hsl(var(--card-foreground));
  border-color: hsl(var(--border));
}

/* Instructions overlay */
.instructions-overlay {
  background: hsl(var(--card) / 0.95);
  color: hsl(var(--card-foreground));
  border-color: hsl(var(--border));
  backdrop-filter: blur(8px);
}

/* Ensure proper text contrast */
.demo-text {
  color: hsl(var(--foreground));
}

.demo-muted-text {
  color: hsl(var(--muted-foreground));
}

/* Button styling in demo */
.demo-button {
  background: hsl(var(--primary));
  color: hsl(var(--primary-foreground));
}

.demo-button:hover {
  background: hsl(var(--primary) / 0.9);
}

/* Badge styling */
.demo-badge {
  background: hsl(var(--secondary));
  color: hsl(var(--secondary-foreground));
  border-color: hsl(var(--border));
}

/* Ensure scrollbars match theme */
.custom-scrollbar::-webkit-scrollbar {
  width: 8px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: hsl(var(--muted));
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: hsl(var(--muted-foreground) / 0.3);
  border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--muted-foreground) / 0.5);
}

/* Loading state styling */
.demo-loading {
  background: hsl(var(--background));
  color: hsl(var(--muted-foreground));
}

/* Ensure proper focus states */
.demo-focus:focus {
  outline: 2px solid hsl(var(--primary));
  outline-offset: 2px;
}

/* Tab styling */
.demo-tabs {
  background: hsl(var(--card));
  border-color: hsl(var(--border));
}

.demo-tab-active {
  background: hsl(var(--background));
  color: hsl(var(--foreground));
}

.demo-tab-inactive {
  color: hsl(var(--muted-foreground));
}

.demo-tab-inactive:hover {
  color: hsl(var(--foreground));
  background: hsl(var(--muted));
}
