"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { createClient } from "@/lib/supabase/client";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, DialogContent, Di<PERSON>Header, DialogTitle } from "@/components/ui/dialog";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { useToast } from "@/hooks/use-toast";
import { 
  ChevronLeft, 
  ChevronRight, 
  Sparkles, 
  BookOpen, 
  Users, 
  Globe,
  Target,
  Settings,
  CheckCircle,
  Plus,
  Wand2
} from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";
import { useCelebration } from "@/contexts/celebration-context";
import { useAuth } from "@/contexts/auth-context";
import { logger } from "@/lib/services/logger";

interface WizardStep {
  id: string;
  title: string;
  description: string;
  icon: React.ComponentType<{ className?: string }>;
}

interface FormData {
  title: string;
  description: string;
  genre: string;
  subgenre: string;
  targetAudience: string;
  wordCount: string;
  chapters: string;
  tone: string;
  protagonist: string;
  antagonist: string;
  setting: string;
  themes: string;
  structure: string;
  pacing: string;
  narrativeVoice: string;
  tense: string;
  [key: string]: string;
}

const wizardSteps: WizardStep[] = [
  { id: "basics", title: "Project Basics", description: "Set up your project foundation", icon: BookOpen },
  { id: "genre", title: "Genre & Style", description: "Define your story's genre and tone", icon: Sparkles },
  { id: "characters", title: "Characters & World", description: "Create your story universe", icon: Users },
  { id: "structure", title: "Structure & Pacing", description: "Plan your narrative flow", icon: Target },
  { id: "technical", title: "Technical Specs", description: "Set word counts and chapters", icon: Settings },
  { id: "review", title: "Review & Generate", description: "Finalize and create your project", icon: CheckCircle }
];

interface StoryCreationModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function StoryCreationModal({ open, onOpenChange }: StoryCreationModalProps) {
  const router = useRouter();
  const { toast } = useToast();
  const { user } = useAuth();
  const { celebrate } = useCelebration();
  const supabase = createClient();
  
  const [currentStep, setCurrentStep] = useState(0);
  const [isGenerating, setIsGenerating] = useState(false);
  const [formData, setFormData] = useState<FormData>({
    title: "",
    description: "",
    genre: "",
    subgenre: "",
    tone: "",
    targetAudience: "adult",
    protagonist: "",
    antagonist: "",
    setting: "",
    wordCount: "80000",
    chapters: "25",
    themes: "",
    structure: "three-act",
    pacing: "medium",
    narrativeVoice: "third-limited",
    tense: "past"
  });

  const progress = ((currentStep + 1) / wizardSteps.length) * 100;

  const nextStep = () => {
    if (currentStep < wizardSteps.length - 1) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const updateFormData = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const canProceed = () => {
    switch (currentStep) {
      case 0:
        return formData.title.trim().length > 0 && formData.description.trim().length > 0;
      case 1:
        return !!(formData.genre && formData.tone);
      case 2:
        return !!(formData.protagonist || formData.setting);
      case 3:
        return !!(formData.structure && formData.pacing);
      case 4:
        return !!(formData.wordCount && formData.chapters);
      case 5:
        return true;
      default:
        return false;
    }
  };

  const handleGenerate = async () => {
    if (!user) {
      toast({
        title: "Not authenticated",
        description: "Please sign in to create a project",
        variant: "destructive"
      });
      return;
    }

    setIsGenerating(true);
    
    try {
      // Create the project with structured data
      const projectData = {
        user_id: user.id,
        title: formData.title,
        description: formData.description,
        primary_genre: formData.genre,
        sub_genre: formData.subgenre,
        tone_style: formData.tone,
        target_audience: formData.targetAudience,
        target_word_count: parseInt(formData.wordCount),
        target_chapters: parseInt(formData.chapters),
        narrative_voice: formData.narrativeVoice,
        tense: formData.tense,
        structure_type: formData.structure,
        pacing_preference: formData.pacing,
        time_period: formData.setting,
        protagonist_name: formData.protagonist,
        themes: formData.themes,
        status: 'active'
      };

      const { data: project, error } = await supabase
        .from('projects')
        .insert([projectData])
        .select()
        .single();

      if (error) throw error;

      // Trigger celebration
      celebrate({
        id: 'project-created',
        type: 'achievement',
        title: 'Project Created!',
        message: `${formData.title} is ready for your imagination`,
        icon: BookOpen,
        level: 'medium'
      });

      // Close modal and navigate
      onOpenChange(false);
      router.push(`/projects/${project.id}`);
      
      toast({
        title: "Project created!",
        description: "Your AI agents are preparing your story structure...",
      });
      
    } catch (error) {
      logger.error('Error creating project:', error);
      toast({
        title: "Error",
        description: "Failed to create project. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsGenerating(false);
    }
  };

  const handleClose = () => {
    if (currentStep > 0) {
      const confirmed = window.confirm("Are you sure you want to cancel? Your progress will be lost.");
      if (!confirmed) return;
    }
    setCurrentStep(0);
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden p-0">
        <DialogHeader className="px-6 pt-6 pb-4 border-b">
          <div className="flex items-center justify-between">
            <DialogTitle className="text-2xl flex items-center gap-2">
              <Wand2 className="w-6 h-6 text-primary" />
              Create Your Story
            </DialogTitle>
            <Badge variant="secondary" className="text-xs">
              AI-Powered Setup
            </Badge>
          </div>
          
          {/* Progress Bar */}
          <div className="space-y-2 mt-4">
            <div className="flex justify-between text-sm text-muted-foreground">
              <span>Step {currentStep + 1} of {wizardSteps.length}</span>
              <span>{Math.round(progress)}% Complete</span>
            </div>
            <Progress value={progress} className="h-2" />
          </div>

          {/* Step Navigation */}
          <div className="flex items-center gap-2 mt-4 overflow-x-auto pb-2">
            {wizardSteps.map((step, index) => {
              const StepIcon = step.icon;
              const isActive = index === currentStep;
              const isCompleted = index < currentStep;
              
              return (
                <button
                  key={step.id}
                  className={`flex items-center gap-2 px-3 py-2 rounded-lg text-sm whitespace-nowrap transition-all ${
                    isActive 
                      ? 'bg-primary text-primary-foreground' 
                      : isCompleted
                      ? 'bg-primary/20 text-primary'
                      : 'bg-muted text-muted-foreground hover:bg-muted/80'
                  }`}
                  onClick={() => isCompleted ? setCurrentStep(index) : null}
                  disabled={!isCompleted && !isActive}
                >
                  <StepIcon className="w-4 h-4" />
                  <span className="hidden sm:inline">{step.title}</span>
                </button>
              );
            })}
          </div>
        </DialogHeader>

        <div className="px-6 py-4 overflow-y-auto" style={{ maxHeight: 'calc(90vh - 280px)' }}>
          <AnimatePresence mode="wait">
            <motion.div
              key={currentStep}
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              transition={{ duration: 0.2 }}
              className="min-h-[400px]"
            >
              <div className="mb-6">
                <h3 className="text-xl font-semibold mb-2">{wizardSteps[currentStep]?.title}</h3>
                <p className="text-muted-foreground">{wizardSteps[currentStep]?.description}</p>
              </div>

              {currentStep === 0 && <BasicsStep formData={formData} updateFormData={updateFormData} />}
              {currentStep === 1 && <GenreStep formData={formData} updateFormData={updateFormData} />}
              {currentStep === 2 && <CharactersStep formData={formData} updateFormData={updateFormData} />}
              {currentStep === 3 && <StructureStep formData={formData} updateFormData={updateFormData} />}
              {currentStep === 4 && <TechnicalStep formData={formData} updateFormData={updateFormData} />}
              {currentStep === 5 && <ReviewStep formData={formData} />}
            </motion.div>
          </AnimatePresence>
        </div>

        <div className="flex justify-between p-6 border-t bg-muted/30">
          <Button
            variant="outline"
            onClick={prevStep}
            disabled={currentStep === 0}
          >
            <ChevronLeft className="w-4 h-4 mr-2" />
            Previous
          </Button>
          
          {currentStep < wizardSteps.length - 1 ? (
            <Button
              onClick={nextStep}
              disabled={!canProceed()}
              className="bg-gradient-to-r from-primary to-primary/80 hover:from-primary/90 hover:to-primary/70"
            >
              Next
              <ChevronRight className="w-4 h-4 ml-2" />
            </Button>
          ) : (
            <Button
              onClick={handleGenerate}
              disabled={!canProceed() || isGenerating}
              className="bg-gradient-to-r from-primary to-primary/80 hover:from-primary/90 hover:to-primary/70"
            >
              {isGenerating ? (
                <>
                  <Sparkles className="w-4 h-4 mr-2 animate-spin" />
                  Generating...
                </>
              ) : (
                <>
                  <Sparkles className="w-4 h-4 mr-2" />
                  Create Story
                </>
              )}
            </Button>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}

// Step Components (simplified versions of the demo components)
function BasicsStep({ formData, updateFormData }: { formData: FormData; updateFormData: (field: string, value: string) => void }) {
  return (
    <div className="space-y-6">
      <div className="space-y-2">
        <Label htmlFor="title">Project Title</Label>
        <Input
          id="title"
          value={formData.title}
          onChange={(e) => updateFormData("title", e.target.value)}
          placeholder="Enter your story title..."
          className="text-lg"
        />
      </div>
      
      <div className="space-y-2">
        <Label htmlFor="description">Story Concept</Label>
        <Textarea
          id="description"
          value={formData.description}
          onChange={(e) => updateFormData("description", e.target.value)}
          className="min-h-[120px]"
          placeholder="Describe your story idea in a few sentences..."
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="audience">Target Audience</Label>
        <Select value={formData.targetAudience} onValueChange={(value) => updateFormData("targetAudience", value)}>
          <SelectTrigger>
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="middle-grade">Middle Grade (8-12)</SelectItem>
            <SelectItem value="young-adult">Young Adult (13-17)</SelectItem>
            <SelectItem value="new-adult">New Adult (18-25)</SelectItem>
            <SelectItem value="adult">Adult (25+)</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div className="p-4 bg-primary/5 border border-primary/20 rounded-lg">
        <div className="flex items-start gap-3">
          <Sparkles className="w-5 h-5 text-primary mt-0.5" />
          <div>
            <h4 className="font-medium text-primary">AI Assistance</h4>
            <p className="text-sm text-muted-foreground mt-1">
              Our AI will help expand your concept into a full story structure with rich characters and plot.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}

function GenreStep({ formData, updateFormData }: { formData: FormData; updateFormData: (field: string, value: string) => void }) {
  const genres = [
    { id: "fantasy", name: "Fantasy", subgenres: ["Epic Fantasy", "Urban Fantasy", "Dark Fantasy", "High Fantasy"] },
    { id: "sci-fi", name: "Science Fiction", subgenres: ["Space Opera", "Cyberpunk", "Dystopian", "Hard Sci-Fi"] },
    { id: "romance", name: "Romance", subgenres: ["Contemporary", "Historical", "Paranormal", "Romantic Suspense"] },
    { id: "mystery", name: "Mystery", subgenres: ["Cozy Mystery", "Police Procedural", "Noir", "Psychological Thriller"] },
    { id: "thriller", name: "Thriller", subgenres: ["Action", "Psychological", "Political", "Techno-thriller"] },
    { id: "literary", name: "Literary Fiction", subgenres: ["Contemporary", "Historical", "Experimental", "Philosophical"] }
  ];

  return (
    <div className="space-y-6">
      <div className="space-y-2">
        <Label>Primary Genre</Label>
        <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
          {genres.map((genre) => (
            <Card
              key={genre.id}
              className={`cursor-pointer transition-all ${
                formData.genre === genre.id
                  ? 'border-primary bg-primary/10'
                  : 'hover:border-primary/50'
              }`}
              onClick={() => updateFormData("genre", genre.id)}
            >
              <CardContent className="p-4 text-center">
                <h4 className="font-medium">{genre.name}</h4>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {formData.genre && (
        <div className="space-y-2">
          <Label>Subgenre</Label>
          <Select value={formData.subgenre} onValueChange={(value) => updateFormData("subgenre", value)}>
            <SelectTrigger>
              <SelectValue placeholder="Select a subgenre" />
            </SelectTrigger>
            <SelectContent>
              {genres.find(g => g.id === formData.genre)?.subgenres.map((sub) => (
                <SelectItem key={sub} value={sub.toLowerCase().replace(/\s+/g, '-')}>
                  {sub}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      )}

      <div className="space-y-2">
        <Label>Tone & Mood</Label>
        <div className="flex flex-wrap gap-2">
          {["Adventurous", "Dark", "Humorous", "Romantic", "Mysterious", "Epic", "Gritty", "Lighthearted", "Philosophical", "Suspenseful"].map((tone) => (
            <Badge
              key={tone}
              variant={formData.tone === tone.toLowerCase() ? "default" : "outline"}
              className={`cursor-pointer ${
                formData.tone === tone.toLowerCase()
                  ? ''
                  : 'hover:bg-primary/20'
              }`}
              onClick={() => updateFormData("tone", tone.toLowerCase())}
            >
              {tone}
            </Badge>
          ))}
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label>Narrative Voice</Label>
          <Select value={formData.narrativeVoice} onValueChange={(value) => updateFormData("narrativeVoice", value)}>
            <SelectTrigger>
              <SelectValue placeholder="Select narrative voice" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="first">First Person</SelectItem>
              <SelectItem value="third-limited">Third Person Limited</SelectItem>
              <SelectItem value="third-omniscient">Third Person Omniscient</SelectItem>
              <SelectItem value="second">Second Person</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <Label>Tense</Label>
          <Select value={formData.tense} onValueChange={(value) => updateFormData("tense", value)}>
            <SelectTrigger>
              <SelectValue placeholder="Select tense" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="past">Past Tense</SelectItem>
              <SelectItem value="present">Present Tense</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>
    </div>
  );
}

function CharactersStep({ formData, updateFormData }: { formData: FormData; updateFormData: (field: string, value: string) => void }) {
  return (
    <div className="space-y-6">
      <div className="space-y-2">
        <Label htmlFor="protagonist">Main Protagonist</Label>
        <Input
          id="protagonist"
          value={formData.protagonist}
          onChange={(e) => updateFormData("protagonist", e.target.value)}
          placeholder="e.g., Sarah Chen, a brilliant detective"
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="antagonist">Antagonist (Optional)</Label>
        <Input
          id="antagonist"
          value={formData.antagonist}
          onChange={(e) => updateFormData("antagonist", e.target.value)}
          placeholder="e.g., The Shadow Corporation"
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="setting">Primary Setting</Label>
        <Input
          id="setting"
          value={formData.setting}
          onChange={(e) => updateFormData("setting", e.target.value)}
          placeholder="e.g., Neo-Tokyo 2087, Victorian London"
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="themes">Core Themes</Label>
        <Input
          id="themes"
          value={formData.themes}
          onChange={(e) => updateFormData("themes", e.target.value)}
          placeholder="e.g., redemption, identity, power"
        />
      </div>

      <div className="p-4 bg-primary/5 border border-primary/20 rounded-lg">
        <div className="flex items-start gap-3">
          <Users className="w-5 h-5 text-primary mt-0.5" />
          <div>
            <h4 className="font-medium text-primary">AI Character Development</h4>
            <p className="text-sm text-muted-foreground mt-1">
              Our Character Developer agent will create detailed profiles with backstories, motivations, and character arcs.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}

function StructureStep({ formData, updateFormData }: { formData: FormData; updateFormData: (field: string, value: string) => void }) {
  const structures = [
    { id: "three-act", name: "Three-Act Structure", desc: "Classic beginning, middle, end" },
    { id: "heros-journey", name: "Hero's Journey", desc: "Campbell's monomyth structure" },
    { id: "seven-point", name: "Seven-Point Story", desc: "Dan Wells' structure method" },
    { id: "save-the-cat", name: "Save the Cat", desc: "Blake Snyder's beat sheet" },
    { id: "freytag", name: "Freytag's Pyramid", desc: "Five-act dramatic structure" },
    { id: "kishotenketsu", name: "Kishōtenketsu", desc: "Four-act Asian narrative structure" }
  ];

  return (
    <div className="space-y-6">
      <div className="space-y-2">
        <Label>Story Structure</Label>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
          {structures.map((structure) => (
            <Card
              key={structure.id}
              className={`cursor-pointer transition-all ${
                formData.structure === structure.id
                  ? 'border-primary bg-primary/10'
                  : 'hover:border-primary/50'
              }`}
              onClick={() => updateFormData("structure", structure.id)}
            >
              <CardContent className="p-4">
                <h4 className="font-medium">{structure.name}</h4>
                <p className="text-sm text-muted-foreground mt-1">{structure.desc}</p>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      <div className="space-y-2">
        <Label>Pacing Preference</Label>
        <div className="flex flex-wrap gap-2">
          {[
            { id: "slow", name: "Slow Burn", desc: "Gradual tension build" },
            { id: "medium", name: "Moderate", desc: "Balanced pacing" },
            { id: "fast", name: "Fast-Paced", desc: "Quick, snappy scenes" },
            { id: "variable", name: "Variable", desc: "Mix of pacing styles" }
          ].map((pace) => (
            <Badge
              key={pace.id}
              variant={formData.pacing === pace.id ? "default" : "outline"}
              className={`cursor-pointer ${
                formData.pacing === pace.id
                  ? ''
                  : 'hover:bg-primary/20'
              }`}
              onClick={() => updateFormData("pacing", pace.id)}
            >
              {pace.name}
            </Badge>
          ))}
        </div>
      </div>
    </div>
  );
}

function TechnicalStep({ formData, updateFormData }: { formData: FormData; updateFormData: (field: string, value: string) => void }) {
  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="space-y-2">
          <Label htmlFor="wordCount">Target Word Count</Label>
          <Select value={formData.wordCount} onValueChange={(value) => updateFormData("wordCount", value)}>
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="20000">20,000 words (Novella)</SelectItem>
              <SelectItem value="50000">50,000 words (Short Novel)</SelectItem>
              <SelectItem value="80000">80,000 words (Standard Novel)</SelectItem>
              <SelectItem value="120000">120,000 words (Epic Novel)</SelectItem>
              <SelectItem value="200000">200,000+ words (Series)</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <Label htmlFor="chapters">Estimated Chapters</Label>
          <Input
            id="chapters"
            type="number"
            value={formData.chapters}
            onChange={(e) => updateFormData("chapters", e.target.value)}
            min="1"
            max="100"
          />
        </div>
      </div>

      <div className="space-y-2">
        <Label>Average Chapter Length</Label>
        <div className="text-sm text-muted-foreground">
          Approximately {Math.round(parseInt(formData.wordCount) / parseInt(formData.chapters)).toLocaleString()} words per chapter
        </div>
      </div>

      <div className="p-4 bg-primary/5 border border-primary/20 rounded-lg">
        <div className="flex items-start gap-3">
          <Target className="w-5 h-5 text-primary mt-0.5" />
          <div>
            <h4 className="font-medium text-primary">Smart Chapter Planning</h4>
            <p className="text-sm text-muted-foreground mt-1">
              AI will optimize chapter breaks for maximum impact and reader engagement based on your pacing preference.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}

function ReviewStep({ formData }: { formData: FormData }) {
  return (
    <div className="space-y-6">
      <div className="text-center mb-6">
        <CheckCircle className="w-16 h-16 text-primary mx-auto mb-4" />
        <h3 className="text-xl font-semibold mb-2">Ready to Create Your Story!</h3>
        <p className="text-muted-foreground">Review your selections below</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-lg">Project Details</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2 text-sm">
            <div><span className="text-muted-foreground">Title:</span> <span className="font-medium">{formData.title}</span></div>
            <div><span className="text-muted-foreground">Genre:</span> <span className="font-medium">{formData.genre}</span></div>
            <div><span className="text-muted-foreground">Tone:</span> <span className="font-medium">{formData.tone}</span></div>
            <div><span className="text-muted-foreground">Audience:</span> <span className="font-medium">{formData.targetAudience}</span></div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-lg">Technical Specs</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2 text-sm">
            <div><span className="text-muted-foreground">Word Count:</span> <span className="font-medium">{parseInt(formData.wordCount).toLocaleString()}</span></div>
            <div><span className="text-muted-foreground">Chapters:</span> <span className="font-medium">{formData.chapters}</span></div>
            <div><span className="text-muted-foreground">Structure:</span> <span className="font-medium">{formData.structure}</span></div>
            <div><span className="text-muted-foreground">Pacing:</span> <span className="font-medium">{formData.pacing}</span></div>
          </CardContent>
        </Card>
      </div>

      <div className="p-6 bg-gradient-to-r from-primary/10 to-primary/5 border border-primary/20 rounded-lg">
        <div className="text-center">
          <Sparkles className="w-8 h-8 text-primary mx-auto mb-3" />
          <h4 className="font-semibold text-primary mb-2">AI Generation Process</h4>
          <p className="text-sm text-muted-foreground mb-4">
            Our AI agents will create your complete story structure, character profiles, and chapter outlines.
          </p>
          <div className="flex flex-wrap justify-center gap-3 text-xs">
            <Badge variant="secondary">Story Architecture</Badge>
            <Badge variant="secondary">Character Development</Badge>
            <Badge variant="secondary">World Building</Badge>
            <Badge variant="secondary">Chapter Planning</Badge>
          </div>
        </div>
      </div>
    </div>
  );
}