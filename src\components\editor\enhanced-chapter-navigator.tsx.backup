'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { useEditorStore } from '@/stores/editor-store'
import { useResizablePanel } from '@/hooks/use-resizable-panel'
import { BookImportDialogSimple } from '@/components/import/book-import-dialog-simple'
import { createClient } from '@/lib/supabase/client'
import { 
  ChevronLeft, 
  ChevronRight, 
  Plus, 
  CheckCircle, 
  Circle, 
  Edit3,
  Search,
  List,
  Target,
  BookOpen,
  TrendingUp,
  Eye,
  ChevronDown,
  ChevronRight as ChevronRightIcon,
  Upload,
  FileText,
  Calendar,
  User,
  MoreVertical,
  PanelLeftClose,
  PanelLeftOpen
} from 'lucide-react'
import { Database } from '@/lib/db/types'

type Project = Database['public']['Tables']['projects']['Row']

interface ChapterNavigatorProps {
  projectId: string
  currentChapterId?: string
  content?: string
  onChapterSelect: (chapterId: string, chapterNumber: number) => void
  onCreateChapter: () => void
  onImportComplete?: () => void
}

interface OutlineItem {
  id: string
  text: string
  level: number
  lineNumber: number
  wordCount: number
}

export function EnhancedChapterNavigator({ 
  projectId,
  currentChapterId, 
  content = '',
  onChapterSelect,
  onCreateChapter,
  onImportComplete 
}: ChapterNavigatorProps) {
  const { 
    showChapterNavigator, 
    toggleChapterNavigator, 
    chapters 
  } = useEditorStore()
  
  const [searchTerm, setSearchTerm] = useState('')
  const [isCollapsed, setIsCollapsed] = useState(false)
  const [outline, setOutline] = useState<OutlineItem[]>([])
  const [expandedOutlineItems, setExpandedOutlineItems] = useState<Set<string>>(new Set())
  const [project, setProject] = useState<Project | null>(null)
  const [isOutlineCollapsed, setIsOutlineCollapsed] = useState(false)
  
  const { width, isResizing, ResizeHandle } = useResizablePanel({
    initialWidth: 320,
    minWidth: 280,
    maxWidth: 480
  })
  
  const supabase = createClient()

  // Load project data
  useEffect(() => {
    loadProject()
  }, [projectId])
  
  const loadProject = async () => {
    const { data } = await supabase
      .from('projects')
      .select('*')
      .eq('id', projectId)
      .single()
    
    if (data) setProject(data)
  }
  
  // Generate outline from content
  useEffect(() => {
    if (content) {
      generateOutline(content)
    }
  }, [content])

  const generateOutline = (text: string) => {
    const lines = text.split('\n')
    const outlineItems: OutlineItem[] = []
    
    lines.forEach((line, index) => {
      const trimmedLine = line.trim()
      
      // Detect headings (markdown style)
      if (trimmedLine.startsWith('#')) {
        const level = (trimmedLine.match(/^#+/) || [''])[0].length
        const text = trimmedLine.replace(/^#+\s*/, '')
        
        if (text.length > 0) {
          outlineItems.push({
            id: `outline-${index}`,
            text,
            level,
            lineNumber: index + 1,
            wordCount: text.split(/\s+/).length
          })
        }
      }
      
      // Detect scene breaks or chapter divisions
      else if (trimmedLine.match(/^[-*=]{3,}$/) || trimmedLine.match(/^\*\s*\*\s*\*$/)) {
        outlineItems.push({
          id: `break-${index}`,
          text: '--- Scene Break ---',
          level: 3,
          lineNumber: index + 1,
          wordCount: 0
        })
      }
      
      // Detect dialogue or action lines that might be significant
      else if (trimmedLine.startsWith('"') && trimmedLine.length > 20) {
        const preview = trimmedLine.substring(0, 50) + (trimmedLine.length > 50 ? '...' : '')
        outlineItems.push({
          id: `dialogue-${index}`,
          text: preview,
          level: 4,
          lineNumber: index + 1,
          wordCount: trimmedLine.split(/\s+/).length
        })
      }
    })
    
    setOutline(outlineItems)
  }

  const filteredChapters = chapters.filter(chapter => 
    chapter.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    chapter.number.toString().includes(searchTerm)
  )

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'complete': return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'review': return <Eye className="h-4 w-4 text-blue-500" />
      case 'writing': return <Edit3 className="h-4 w-4 text-yellow-500" />
      default: return <Circle className="h-4 w-4 text-gray-400" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'complete': return 'text-green-600 bg-green-50'
      case 'review': return 'text-blue-600 bg-blue-50'
      case 'writing': return 'text-yellow-600 bg-yellow-50'
      default: return 'text-gray-600 bg-gray-50'
    }
  }

  const toggleOutlineItem = (itemId: string) => {
    const newExpanded = new Set(expandedOutlineItems)
    if (newExpanded.has(itemId)) {
      newExpanded.delete(itemId)
    } else {
      newExpanded.add(itemId)
    }
    setExpandedOutlineItems(newExpanded)
  }

  const totalWords = chapters.reduce((sum, chapter) => sum + chapter.wordCount, 0)
  const completedChapters = chapters.filter(ch => ch.status === 'complete').length

  if (!showChapterNavigator) return null
  
  if (isCollapsed) {
    return (
      <div className="h-full w-12 border-r bg-background flex flex-col items-center py-4">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => setIsCollapsed(false)}
          className="mb-4"
        >
          <PanelLeftOpen className="h-4 w-4" />
        </Button>
        
        <div className="flex-1 flex flex-col items-center gap-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsCollapsed(false)}
            className="p-2"
          >
            <FileText className="h-4 w-4" />
          </Button>
          
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsCollapsed(false)}
            className="p-2"
          >
            <List className="h-4 w-4" />
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="h-full flex flex-col bg-background border-r relative panel-transition resizable-panel custom-scrollbar" style={{ width: `${width}px` }}>
      <ResizeHandle />
      
      {/* Project Header */}
      <div className="border-b p-4 space-y-3">
        <div className="flex items-start justify-between">
          <div className="flex-1 min-w-0">
            <h2 className="font-semibold text-lg truncate">
              {project?.title || 'Loading...'}
            </h2>
            <div className="flex items-center gap-4 text-sm text-muted-foreground mt-1">
              <div className="flex items-center gap-1">
                <User className="h-3 w-3" />
                <span>{project?.genre || 'Fiction'}</span>
              </div>
              <div className="flex items-center gap-1">
                <Calendar className="h-3 w-3" />
                <span>{new Date(project?.created_at || Date.now()).toLocaleDateString()}</span>
              </div>
            </div>
          </div>
          <div className="flex items-center gap-1">
            <Button 
              variant="ghost" 
              size="sm"
              onClick={() => setIsCollapsed(true)}
            >
              <PanelLeftClose className="h-4 w-4" />
            </Button>
            <Button variant="ghost" size="sm" onClick={toggleChapterNavigator}>
              <ChevronLeft className="h-4 w-4" />
            </Button>
          </div>
        </div>
        
        {/* Quick Stats */}
        <div className="grid grid-cols-3 gap-2">
          <div className="text-center p-2 bg-muted rounded-md">
            <div className="text-lg font-semibold">{chapters.length}</div>
            <div className="text-xs text-muted-foreground">Chapters</div>
          </div>
          <div className="text-center p-2 bg-muted rounded-md">
            <div className="text-lg font-semibold">
              {Math.round(totalWords / 1000)}k
            </div>
            <div className="text-xs text-muted-foreground">Words</div>
          </div>
          <div className="text-center p-2 bg-muted rounded-md">
            <div className="text-lg font-semibold">
              {Math.round((completedChapters / Math.max(chapters.length, 1)) * 100)}%
            </div>
            <div className="text-xs text-muted-foreground">Complete</div>
          </div>
        </div>
        
        {/* Progress Bar */}
        <div className="space-y-1">
          <div className="flex items-center justify-between text-xs">
            <span className="text-muted-foreground">Overall Progress</span>
            <span className="font-medium">{completedChapters}/{chapters.length}</span>
          </div>
          <Progress value={(completedChapters / Math.max(chapters.length, 1)) * 100} className="h-2" />
        </div>
      </div>

      {/* Fixed Layout: Chapters at top, Collapsible Outline at bottom */}
      <div className="flex-1 flex flex-col overflow-hidden px-4">
        {/* Chapters Section - Fixed at Top */}
        <div className="flex-shrink-0">
          <div className="space-y-3 mb-4">
            <div className="flex items-center justify-between">
              <h3 className="font-medium text-sm flex items-center gap-2">
                <FileText className="w-4 h-4" />
                Chapters
              </h3>
            </div>
            
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search chapters..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 h-9"
              />
            </div>
            
            <div className="flex gap-2">
              <BookImportDialogSimple 
                projectId={projectId}
                onImportComplete={() => {
                  onImportComplete?.()
                }}
                trigger={
                  <Button variant="outline" size="sm" className="flex-1 hover-lift">
                    <Upload className="h-4 w-4 mr-2" />
                    Import Book
                  </Button>
                }
              />
              <Button onClick={onCreateChapter} size="sm" className="flex-1 hover-lift">
                <Plus className="h-4 w-4 mr-2" />
                New Chapter
              </Button>
            </div>
          </div>

          {/* Chapter List */}
          <ScrollArea className={`-mx-4 px-4 custom-scrollbar ${isOutlineCollapsed ? 'h-96' : 'h-64'}`}>
            <div className="space-y-2 pb-4 content-fade">
                  {filteredChapters.map((chapter) => (
                    <div
                      key={chapter.id}
                      className={`chapter-item group relative p-3 rounded-lg cursor-pointer transition-all hover:bg-muted/50 ${
                        currentChapterId === chapter.id 
                          ? 'active bg-primary/10 border-l-4 border-primary' 
                          : 'hover:border-l-4 hover:border-muted-foreground/20'
                      }`}
                      onClick={() => onChapterSelect(chapter.id, chapter.number)}
                    >
                      <div className="space-y-1.5">
                        <div className="flex items-start justify-between">
                          <div className="flex items-center gap-2 min-w-0">
                            {getStatusIcon(chapter.status)}
                            <div className="min-w-0">
                              <div className="flex items-center gap-2">
                                <span className="font-medium text-sm">
                                  Chapter {chapter.number}
                                </span>
                                {chapter.title !== `Chapter ${chapter.number}` && (
                                  <span className="text-sm text-muted-foreground truncate">
                                    · {chapter.title}
                                  </span>
                                )}
                              </div>
                            </div>
                          </div>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-6 w-6 p-0 opacity-0 group-hover:opacity-100"
                            onClick={(e) => {
                              e.stopPropagation()
                              // Add chapter menu functionality here
                            }}
                          >
                            <MoreVertical className="h-3 w-3" />
                          </Button>
                        </div>
                        
                        <div className="flex items-center justify-between text-xs">
                          <Badge 
                            variant="outline" 
                            className={`${getStatusColor(chapter.status)} border-0`}
                          >
                            {chapter.status}
                          </Badge>
                          <span className="text-muted-foreground">
                            {chapter.wordCount.toLocaleString()} words
                          </span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </ScrollArea>
            </div>
          </div>

          {/* Collapsible Outline Section */}
          <div className="flex-1 flex flex-col min-h-0 mt-4">
            <div 
              className="flex items-center justify-between cursor-pointer p-2 hover:bg-muted/50 rounded-lg"
              onClick={() => setIsOutlineCollapsed(!isOutlineCollapsed)}
            >
              <h3 className="font-medium text-sm flex items-center gap-2">
                <BookOpen className="w-4 h-4" />
                Current Chapter Outline
              </h3>
              <div className="flex items-center gap-2">
                <Badge variant="secondary" className="text-xs">
                  {outline.length} items
                </Badge>
                <ChevronDown 
                  className={`w-4 h-4 transition-transform duration-200 ${
                    isOutlineCollapsed ? 'rotate-180' : ''
                  }`}
                />
              </div>
            </div>
            
            {!isOutlineCollapsed && (
              <div className="flex-1 overflow-hidden mt-2">
                <ScrollArea className="h-full -mx-4 px-4 custom-scrollbar">
                  {outline.length === 0 ? (
                    <div className="text-center py-8 text-muted-foreground">
                      <List className="h-8 w-8 mx-auto mb-2 opacity-50" />
                      <p className="text-sm">No outline detected</p>
                      <p className="text-xs mt-1">
                        Use headings (# ## ###) to create structure
                      </p>
                    </div>
                  ) : (
                    <div className="space-y-1">
                      {outline.map((item) => (
                        <div 
                          key={item.id}
                          className="flex items-center gap-2 p-2 rounded hover:bg-muted cursor-pointer"
                          style={{ paddingLeft: `${(item.level - 1) * 12 + 8}px` }}
                        >
                          {item.level <= 3 && (
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-4 w-4 p-0"
                              onClick={() => toggleOutlineItem(item.id)}
                            >
                              {expandedOutlineItems.has(item.id) ? 
                                <ChevronDown className="h-3 w-3" /> : 
                                <ChevronRightIcon className="h-3 w-3" />
                              }
                            </Button>
                          )}
                          
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center gap-2">
                              <span className={`text-xs font-medium ${
                                item.level === 1 ? 'text-lg' :
                                item.level === 2 ? 'text-base' :
                                item.level === 3 ? 'text-sm' :
                                'text-xs text-muted-foreground'
                              }`}>
                                {item.text}
                              </span>
                            </div>
                            
                            <div className="flex items-center gap-2 mt-1">
                              <Badge variant="outline" className="text-xs">
                                Line {item.lineNumber}
                              </Badge>
                              {item.wordCount > 0 && (
                                <span className="text-xs text-muted-foreground">
                                  {item.wordCount} words
                                </span>
                              )}
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </ScrollArea>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}