export default function DebugPage() {
  return (
    <div style={{ padding: '20px' }}>
      <h1 style={{ fontSize: '24px', marginBottom: '20px' }}>Debug Page</h1>
      
      <div style={{ marginBottom: '20px' }}>
        <h2 style={{ fontSize: '18px', marginBottom: '10px' }}>Environment Check:</h2>
        <ul style={{ listStyle: 'disc', marginLeft: '20px' }}>
          <li>NODE_ENV: {process.env.NODE_ENV}</li>
          <li>Demo Mode: {process.env.NEXT_PUBLIC_DEMO_MODE || 'not set'}</li>
          <li>Page loaded successfully</li>
        </ul>
      </div>

      <div style={{ marginTop: '20px' }}>
        <h2 style={{ fontSize: '18px', marginBottom: '10px' }}>Quick Links:</h2>
        <ul style={{ listStyle: 'none' }}>
          <li style={{ marginBottom: '10px' }}>
            <a href="/test" style={{ color: 'blue', textDecoration: 'underline' }}>
              Test Page (Basic)
            </a>
          </li>
          <li style={{ marginBottom: '10px' }}>
            <a href="/knowledge-demo-simple" style={{ color: 'blue', textDecoration: 'underline' }}>
              Knowledge Demo Simple
            </a>
          </li>
          <li style={{ marginBottom: '10px' }}>
            <a href="/test-components" style={{ color: 'blue', textDecoration: 'underline' }}>
              Component Tests
            </a>
          </li>
        </ul>
      </div>

      <div style={{ marginTop: '40px', padding: '20px', backgroundColor: '#f0f0f0', borderRadius: '8px' }}>
        <p>If you see this page, the basic React rendering is working.</p>
        <p>Check the browser console (F12) for any error messages.</p>
      </div>
    </div>
  )
}