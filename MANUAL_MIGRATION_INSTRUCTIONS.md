
# Manual Database Migration Instructions

Since automated migration encountered limitations, please follow these steps:

## Step 1: Access Supabase SQL Editor
1. Go to https://supabase.com/dashboard/project/xvqeiwrpbzpiqvwuvtpj
2. Navigate to "SQL Editor" in the left sidebar

## Step 2: Enable Extensions
Run this first:
```sql
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS vector;
```

## Step 3: Run Migration
Copy and paste the entire contents of:
`/mnt/c/Users/<USER>/BookScribe/supabase/consolidated_migration.sql`

Into the SQL Editor and click "Run".

## Expected Result
You should see 28 tables created:
- Authentication: profiles, user_subscriptions
- Core: projects, chapters, characters, story_arcs
- AI Features: ai_suggestions, content_embeddings
- Analytics: writing_sessions, writing_goals, usage_tracking
- And many more...

## Verification
After running the migration, you can verify by running:
```sql
SELECT table_name FROM information_schema.tables 
WHERE table_schema = 'public' 
ORDER BY table_name;
```

You should see all 28 tables listed.
